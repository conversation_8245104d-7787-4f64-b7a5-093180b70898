.TH WEBBENCH 1 "14 Jan 2004"
.\" NAME should be all caps, SECTION should be 1-8, maybe w/ subsection
.\" other parms are allowed: see man(7), man(1)
.SH NAME
webbench \- simple forking web benchmark
.SH SYNOPSIS
.B webbench
.I "[options] URL"
.br
.SH "AUTHOR"
This program and manual page was written by <PERSON><PERSON><PERSON>,
for the
.B Supreme Personality of Godhead
(but may be used by others).
.SH "DESCRIPTION"
.B webbench
is simple program for benchmarking HTTP servers or any
other servers, which can be accessed via HTTP proxy. Unlike others
benchmarks,
.B webbench
uses multiple processes for simulating traffic
generated by multiple users. This allows better operating
on SMP systems and on systems with slow or buggy implementation
of select().
.SH OPTIONS
The programs follow the usual GNU command line syntax, with long
options starting with two dashes (`-').
A summary of options are included below.
.TP
.B \-?, \-h, \-\-help
Show summary of options.
.TP
.B \-v, \-\-version
Show version of program.
.TP
.B \-f, \-\-force
Do not wait for any response from server. Close connection after
request is send. This option produce quite a good denial of service
attack.
.TP
.B \-9, \-\-http09
Use HTTP/0.9 protocol, if possible.
.TP
.B \-1, \-\-http10
Use HTTP/1.0 protocol, if possible.
.TP
.B \-2, \-\-http11
Use HTTP/1.1 protocol (without
.I Keep-Alive
), if possible.
.TP
.B \-r, \-\-reload
Forces proxy to reload document. If proxy is not
set, option has no effect.
.TP
.B \-t, \-\-time <n>
Run benchmark for
.I <n>
seconds. Default value is 30.
.TP
.B \-p, \-\-proxy <server:port>
Send request via proxy server. Needed for supporting others protocols
than HTTP.
.TP
.B \-\-get
Use GET request method.
.TP
.B \-\-head
Use HEAD request method.
.TP
.B \-\-options
Use OPTIONS request method.
.TP
.B \-\-trace
Use TRACE request method.
.TP
.B \-c, \-\-clients <n>
Use
.I <n>
multiple clients for benchmark. Default value
is 1.
.SH "EXIT STATUS"
.TP
0 - sucess
.TP
1 - benchmark failed, can not connect to server
.TP
2 - bad command line argument(s)
.TP
3 - internal error, i.e. fork failed
.SH "TODO"
Include support for using
.I Keep-Alive
HTTP/1.1 connections.
.SH "COPYING"
Webbench is distributed under GPL. Copyright 1997-2004
Radim Kolar (<EMAIL>). 
UNIX sockets code taken from popclient 1.5 4/1/94 
public domain code, created by Virginia Tech Computing Center.
.BR
This man page is public domain.
