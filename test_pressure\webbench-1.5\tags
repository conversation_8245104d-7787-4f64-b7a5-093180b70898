!_TAG_FILE_FORMAT	2	/extended format; --format=1 will not append ;" to lines/
!_TAG_FILE_SORTED	1	/0=unsorted, 1=sorted, 2=foldcase/
!_TAG_PROGRAM_AUTHOR	<PERSON>	/<EMAIL>/
!_TAG_PROGRAM_NAME	Exuberant Ctags	//
!_TAG_PROGRAM_URL	http://ctags.sourceforge.net	/official site/
!_TAG_PROGRAM_VERSION	5.9~svn20110310	//
METHOD_GET	webbench.c	35;"	d	file:
METHOD_HEAD	webbench.c	36;"	d	file:
METHOD_OPTIONS	webbench.c	37;"	d	file:
METHOD_TRACE	webbench.c	38;"	d	file:
PROGRAM_VERSION	webbench.c	39;"	d	file:
REQUEST_SIZE	webbench.c	50;"	d	file:
Socket	socket.c	/^int Socket(const char *host, int clientPort)$/;"	f
alarm_handler	webbench.c	/^static void alarm_handler(int signal)$/;"	f	file:
bench	webbench.c	/^static int bench(void)$/;"	f	file:
benchcore	webbench.c	/^void benchcore(const char *host,const int port,const char *req)$/;"	f
benchtime	webbench.c	/^int benchtime=30;$/;"	v
build_request	webbench.c	/^void build_request(const char *url)$/;"	f
bytes	webbench.c	/^int bytes=0;$/;"	v
clients	webbench.c	/^int clients=1;$/;"	v
failed	webbench.c	/^int failed=0;$/;"	v
force	webbench.c	/^int force=0;$/;"	v
force_reload	webbench.c	/^int force_reload=0;$/;"	v
host	webbench.c	/^char host[MAXHOSTNAMELEN];$/;"	v
http10	webbench.c	/^int http10=1; \/* 0 - http\/0.9, 1 - http\/1.0, 2 - http\/1.1 *\/$/;"	v
long_options	webbench.c	/^static const struct option long_options[]=$/;"	v	typeref:struct:option	file:
main	webbench.c	/^int main(int argc, char *argv[])$/;"	f
method	webbench.c	/^int method=METHOD_GET;$/;"	v
mypipe	webbench.c	/^int mypipe[2];$/;"	v
proxyhost	webbench.c	/^char *proxyhost=NULL;$/;"	v
proxyport	webbench.c	/^int proxyport=80;$/;"	v
request	webbench.c	/^char request[REQUEST_SIZE];$/;"	v
speed	webbench.c	/^int speed=0;$/;"	v
timerexpired	webbench.c	/^volatile int timerexpired=0;$/;"	v
usage	webbench.c	/^static void usage(void)$/;"	f	file:
